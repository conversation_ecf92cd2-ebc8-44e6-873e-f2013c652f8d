..\djongo\__init__.py
..\djongo\__pycache__\__init__.cpython-310.pyc
..\djongo\__pycache__\admin.cpython-310.pyc
..\djongo\__pycache__\base.cpython-310.pyc
..\djongo\__pycache__\compiler.cpython-310.pyc
..\djongo\__pycache__\creation.cpython-310.pyc
..\djongo\__pycache__\cursor.cpython-310.pyc
..\djongo\__pycache__\database.cpython-310.pyc
..\djongo\__pycache__\exceptions.cpython-310.pyc
..\djongo\__pycache__\features.cpython-310.pyc
..\djongo\__pycache__\introspection.cpython-310.pyc
..\djongo\__pycache__\operations.cpython-310.pyc
..\djongo\__pycache__\schema.cpython-310.pyc
..\djongo\__pycache__\storage.cpython-310.pyc
..\djongo\__pycache__\transaction.cpython-310.pyc
..\djongo\admin.py
..\djongo\base.py
..\djongo\compiler.py
..\djongo\creation.py
..\djongo\cursor.py
..\djongo\database.py
..\djongo\dynamic_formsets\__init__.py
..\djongo\dynamic_formsets\__pycache__\__init__.cpython-310.pyc
..\djongo\dynamic_formsets\__pycache__\apps.cpython-310.pyc
..\djongo\dynamic_formsets\apps.py
..\djongo\dynamic_formsets\static\dynamic_formsets\images\add.png
..\djongo\dynamic_formsets\static\dynamic_formsets\images\delete.png
..\djongo\dynamic_formsets\static\dynamic_formsets\js\jquery-formset\jquery.formset.js
..\djongo\dynamic_formsets\static\dynamic_formsets\js\jquery-formset\jquery.formset.min.js
..\djongo\dynamic_formsets\static\dynamic_formsets\js\jquery\jquery.js
..\djongo\dynamic_formsets\static\dynamic_formsets\js\jquery\jquery.min.js
..\djongo\dynamic_formsets\static\dynamic_formsets\js\jquery\jquery.min.map
..\djongo\dynamic_formsets\static\dynamic_formsets\js\jquery\jquery.slim.js
..\djongo\dynamic_formsets\static\dynamic_formsets\js\jquery\jquery.slim.min.js
..\djongo\dynamic_formsets\static\dynamic_formsets\js\jquery\jquery.slim.min.map
..\djongo\dynamic_formsets\templates\admin\change_form.html
..\djongo\dynamic_formsets\templatetags\__init__.py
..\djongo\dynamic_formsets\templatetags\__pycache__\__init__.cpython-310.pyc
..\djongo\dynamic_formsets\templatetags\__pycache__\formset_tags.cpython-310.pyc
..\djongo\dynamic_formsets\templatetags\formset_tags.py
..\djongo\exceptions.py
..\djongo\features.py
..\djongo\introspection.py
..\djongo\models\__init__.py
..\djongo\models\__pycache__\__init__.cpython-310.pyc
..\djongo\models\__pycache__\fields.cpython-310.pyc
..\djongo\models\__pycache__\indexes.cpython-310.pyc
..\djongo\models\__pycache__\json.cpython-310.pyc
..\djongo\models\fields.py
..\djongo\models\indexes.py
..\djongo\models\json.py
..\djongo\operations.py
..\djongo\schema.py
..\djongo\sql2mongo\__init__.py
..\djongo\sql2mongo\__pycache__\__init__.cpython-310.pyc
..\djongo\sql2mongo\__pycache__\aggregation.cpython-310.pyc
..\djongo\sql2mongo\__pycache__\constraints.cpython-310.pyc
..\djongo\sql2mongo\__pycache__\converters.cpython-310.pyc
..\djongo\sql2mongo\__pycache__\functions.cpython-310.pyc
..\djongo\sql2mongo\__pycache__\operators.cpython-310.pyc
..\djongo\sql2mongo\__pycache__\query.cpython-310.pyc
..\djongo\sql2mongo\__pycache__\sql_tokens.cpython-310.pyc
..\djongo\sql2mongo\__pycache__\validation.cpython-310.pyc
..\djongo\sql2mongo\aggregation.py
..\djongo\sql2mongo\constraints.py
..\djongo\sql2mongo\converters.py
..\djongo\sql2mongo\functions.py
..\djongo\sql2mongo\operators.py
..\djongo\sql2mongo\query.py
..\djongo\sql2mongo\sql_tokens.py
..\djongo\sql2mongo\validation.py
..\djongo\storage.py
..\djongo\transaction.py
PKG-INFO
SOURCES.txt
dependency_links.txt
requires.txt
top_level.txt
