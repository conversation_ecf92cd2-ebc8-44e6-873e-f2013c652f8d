Metadata-Version: 2.1
Name: djongo
Version: 1.3.6
Summary: Driver for allowing Django to use MongoDB as the database backend.
Home-page: https://nesdis.github.io/djongo/
Author: nesdis
Author-email: <EMAIL>
License: AGPL
Keywords: Django MongoDB driver connector
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python :: 3.6
Requires-Python: >=3.6
Provides-Extra: json



Use Mongodb as a backend database for your django project, without changing a
single django model!

Usage
-----

1. Install djongo::

      pip install djongo

2. Into settings.py file of your project, add::

      DATABASES = {
           'default': {
               'ENGINE': 'djongo',
               'NAME': 'your-db-name',
           }
       }

3. Run (ONLY the first time to create collections in mongoDB)::

      manage.py makemigrations
      manage.py migrate

YOUR ARE SET! HAVE FUN!

Requirements
------------

1. <PERSON><PERSON><PERSON> requires python 3.6 or above.


How it works
------------

<PERSON><PERSON>o is a SQL to mongodb query transpiler. It translates a SQL query string
into a mongoDB query document. As a result, all Django features, models etc
work as is.

Django contrib modules::

    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.sessions',

and others... fully supported.

Important links
---------------

* `Full Documentation <https://nesdis.github.io/djongo/>`_
* `Source code <https://github.com/nesdis/djongo>`_
